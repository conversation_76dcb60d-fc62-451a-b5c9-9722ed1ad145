name: compass_app
description: >-
  A sample app that helps users build and book itineraries for trips.
publish_to: none
version: 0.1.0
resolution: workspace

environment:
  sdk: ^3.9.0-0

dependencies:
  cached_network_image: ^3.4.1
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_svg: ^2.0.16
  freezed_annotation: ^2.4.4
  go_router: ^16.0.0
  google_fonts: ^6.2.1
  intl: any
  json_annotation: ^4.9.0
  logging: ^1.3.0
  provider: ^6.1.2
  share_plus: ^10.1.3
  shared_preferences: ^2.3.5

dev_dependencies:
  analysis_defaults:
    path: ../../analysis_defaults
  flutter_test:
    sdk: flutter
  mocktail_image_network: ^1.2.0
  mocktail: ^1.0.4
  integration_test:
    sdk: flutter
  build_runner: ^2.4.14
  freezed: ^3.0.0
  json_serializable: ^6.9.0

flutter:
  uses-material-design: true
  assets:
    - assets/activities.json
    - assets/destinations.json
    - assets/logo.svg
    - assets/user.jpg
