// Copyright 2024 The Flutter team. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:compass_app/domain/models/destination/destination.dart';

const kDestination1 = Destination(
  ref: 'ref1',
  name: 'name1',
  country: 'country1',
  continent: 'Europe',
  knownFor: 'knownFor1',
  tags: ['tags1'],
  imageUrl: 'imageUrl1',
);

const kDestination2 = Destination(
  ref: 'ref2',
  name: 'name2',
  country: 'country2',
  continent: 'Europe',
  knownFor: 'knownFor2',
  tags: ['tags2'],
  imageUrl: 'imageUrl2',
);
