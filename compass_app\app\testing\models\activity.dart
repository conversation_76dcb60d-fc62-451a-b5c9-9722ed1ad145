// Copyright 2024 The Flutter team. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:compass_app/domain/models/activity/activity.dart';

const kActivity = Activity(
  description: 'DESCRIPTION',
  destinationRef: 'DESTINATION',
  duration: 3,
  familyFriendly: true,
  imageUrl: 'http://example.com/img.png',
  locationName: 'LOCATION NAME',
  name: 'NAME',
  price: 3,
  ref: 'REF',
  timeOfDay: TimeOfDay.afternoon,
);
